<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>ECharts</title>
    <!-- 引入 ECharts 文件 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
</head>
<body>
    <!-- 为 ECharts 准备一个具备大小（宽高）的 DOM -->
    <div id="main" style="width: 600px;height:400px;"></div>
    <script type="text/javascript">
        var chartDom = document.getElementById('main');
        var myChart = echarts.init(chartDom);
        var option;

        option = {
                  "title": {"text": "类别数值分布"},
                  "tooltip": {"trigger": "item", "formatter": "{b}: {c} ({d}%)"},
                  "legend": {"data": ["苹果", "香蕉", "橙子", "葡萄"]},
                  "series": [{
                    "name": "数值",
                    "type": "pie",
                    "radius": "50%",
                    "data": [
                      {"value": 40, "name": "苹果"},
                      {"value": 30, "name": "香蕉"},
                      {"value": 20, "name": "橙子"},
                      {"value": 10, "name": "葡萄"}
                    ]
                  }]
                }
                ;

        option && myChart.setOption(option);
    </script>
</body>
</html>