import os

from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 模型客户端配置#############################################################
def qwen3_client():
    """设置Qwen3模型客户端"""
    model_config = {
        "model": os.getenv("QWEN3_MODEL"),
        "api_key": os.getenv("QWEN3_API_KEY"),
        "base_url": os.getenv("QWEN3_API_BASE"),
        "model_info": {
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": "unknown",
            "multiple_system_messages": True
        },
        "enable_thinking": False, #非思考模式
    }
    return OpenAIChatCompletionClient(**model_config)

def deepseek_client():
    """设置DeepSeek模型客户端"""
    model_config = {
        "model": os.getenv("DEEPSEEK_MODEL"),
        "api_key": os.getenv("DEEPSEEK_API_KEY"),
        "base_url": os.getenv("DEEPSEEK_API_BASE"),
        "model_info": {
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": ModelFamily.UNKNOWN,
            "multiple_system_messages": True,
        },
    }
    return OpenAIChatCompletionClient(**model_config)