import asyncio
from typing import List, Sequence

from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.conditions import MaxMessageTermination, TextMentionTermination
from autogen_agentchat.messages import BaseAgentEvent, BaseChatMessage
from autogen_agentchat.teams import Selector<PERSON>roupChat
from autogen_agentchat.ui import <PERSON>sol<PERSON>
from autogen_ext.models.openai import OpenAIChatCompletionClient
from llms import model_client
from langchain_community.tools import DuckDuckGoSearchResults, DuckDuckGoSearchRun

# Note: This example uses mock tools instead of real APIs for demonstration purposes
def search_web_tool(query: str) -> str:
    if "2006-2007" in query:
        return """Here are the total points scored by Miami Heat players in the 2006-2007 season:
        <PERSON><PERSON><PERSON>: 844 points
        <PERSON><PERSON>: 1397 points
        <PERSON>: 550 points
        ...
        """
    elif "2007-2008" in query:
        return "The number of total rebounds for <PERSON><PERSON> in the Miami Heat season 2007-2008 is 214."
    elif "2008-2009" in query:
        return "The number of total rebounds for <PERSON><PERSON> in the Miami Heat season 2008-2009 is 398."
    return "No data found."

async def web_search(query: str) -> str:
    """从互联网查询信息"""
    search = DuckDuckGoSearchRun()
    result = search.invoke(query)  # 注意：旧版本可能用 .run() 而不是 .invoke()
    print(result)
    return result


def percentage_change_tool(start: float, end: float) -> float:
    return ((end - start) / start) * 100


def selector_func(messages: Sequence[BaseAgentEvent | BaseChatMessage]) -> str | None:
    if messages[-1].source != planning_agent.name:
        return planning_agent.name
    return None

#agnets
planning_agent = AssistantAgent(
    "PlanningAgent",
    description="An agent for planning tasks, this agent should be the first to engage when given a new task.",
    model_client=model_client,
    model_client_stream=True,
    system_message="""
    You are a planning agent.
    Your job is to break down complex tasks into smaller, manageable subtasks.
    Your team members are:
        WebSearchAgent: Searches for information
        DataAnalystAgent: Performs calculations

    You only plan and delegate tasks - you do not execute them yourself.

    When assigning tasks, use this format:
    1. <agent> : <task>

    
    """,
)
#当所有的task都完成后，总结结果并且说"TERMINATE"，除此之外不要说"TERMINATE"，不然你会触发终止条件，提前结束
#After all tasks are complete, summarize the findings and end with "TERMINATE".


web_search_agent = AssistantAgent(
    "WebSearchAgent",
    description="An agent for searching information on the web.",
    tools=[search_web_tool],
    model_client=model_client,
    model_client_stream=True,
    system_message="""
    You are a web search agent.
    Your only tool is search_tool - use it to find information.
    You make only one search call at a time.
    Once you have the results, you never do calculations based on them.
    """,
)

data_analyst_agent = AssistantAgent(
    "DataAnalystAgent",
    description="An agent for performing calculations.",
    model_client=model_client,
    tools=[percentage_change_tool],
    model_client_stream=True,
    system_message="""
    You are a data analyst.
    Given the tasks you have been assigned, you should analyze the data and provide results using the tools provided.
    If you have not seen the data, ask for it.
    """,
)

#Termination Conditions
# Let’s use two termination conditions: TextMentionTermination to end the conversation when the Planning Agent sends “TERMINATE”, and MaxMessageTermination to limit the conversation to 25 messages to avoid infinite loop.
text_mention_termination = TextMentionTermination("TERMINATE")
max_messages_termination = MaxMessageTermination(max_messages=25)
termination = text_mention_termination | max_messages_termination
# max_messages_termination = MaxMessageTermination(max_messages=25)
# termination = max_messages_termination

#Selector Prompt
#1.SelectorGroupChat uses a model to select the next speaker based on the conversation context. We will use a custom selector prompt to properly align with the workflow.

#2.The string variables available in the selector prompt are:
#   {participants}: The names of candidates for selection. The format is ["<name1>", "<name2>", ...].
#   {participants} ：待选候选人的姓名。格式为 ["<name1>", "<name2>", ...] 。
#   {roles}: A newline-separated list of names and descriptions of the candidate agents. The format for each line is: "<name> : <description>".
#   {roles} ：以换行符分隔的候选代理的名称和描述列表。每行的格式为： "<name> : <description>" 。
#   {history}: The conversation history formatted as a double newline separated of names and message content. The format for each message is: "<name> : <message content>".
#   {history} ：对话历史记录，格式为双换行符，由姓名和消息内容分隔。每条消息的格式为： "<name> : <message content>" 。"""
selector_prompt = """Select an agent to perform task.

$roles

Current conversation context:
$history

Read the above conversation, then select an agent from $participants to perform the next task.
Make sure the planner agent has assigned tasks before other agents start working.
Only select one agent.
"""


# Running the Team
#let’s create the team with the agents, termination conditions, and custom selector prompt.
# team = SelectorGroupChat(
#     [planning_agent, web_search_agent, data_analyst_agent],
#     model_client=model_client,
#     termination_condition=termination,
#     selector_prompt=selector_prompt,
#     model_client_streaming=True,
#     allow_repeated_speaker=False,  # Allow an agent to speak multiple turns in a row.
# )

# Reset the previous team and run the chat again with the selector function.
team = SelectorGroupChat(
    [planning_agent, web_search_agent, data_analyst_agent],
    model_client=model_client,
    termination_condition=termination,
    selector_prompt=selector_prompt,
    allow_repeated_speaker=True,
    selector_func=selector_func,
)




task = "Who was the Miami Heat player with the highest points in the 2006-2007 season, and what was the percentage change in his total rebounds between the 2007-2008 and 2008-2009 seasons?"

# async def test():
#     await team.run(task=task)

async def test_run_stream() -> None:
    # Option 1: read each message from the stream (as shown in the previous example).
    # async for message in team.run_stream(task="Find information on AutoGen"):
    #     print(message)

    # Option 2: use Console to print all messages as they appear.
    await Console(
        team.run_stream(task=task),
        output_stats=True,  # Enable stats printing.
    )



asyncio.run(test_run_stream())
# asyncio.run(web_search("Obama's first name?"))









