import asyncio

from autogen_core.models import ModelFamily
from dotenv import load_dotenv
import os
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import StructuredMessage
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient

load_dotenv()

def qwen3_client():
    """设置模型客户端"""
    model_config = {
        "model": os.getenv("QWEN3_MODEL"),
        "api_key":os.getenv("QWEN3_API_KEY") ,
        "base_url":os.getenv("QWEN3_API_BASE"),
        "model_info": {
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": "unknown",
            "multiple_system_messages": True
        },
    }



    return OpenAIChatCompletionClient(**model_config)

def deepseek_client():
    """设置模型客户端"""
    model_config = {
        "model": os.getenv("DEEPSEEK_MODEL"),
        "api_key": os.getenv("DEEPSEEK_API_KEY"),
        "base_url": os.getenv("DEEPSEEK_API_BASE"),
        "model_info": {
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": ModelFamily.UNKNOWN,
            "multiple_system_messages": True,

        },
    }
    return OpenAIChatCompletionClient(**model_config)



# model_client = qwen3_client()
model_client = deepseek_client()


if __name__ == "__main__":
    async def web_search(query: str) -> str:
        """Find information on the web"""
        return "AutoGen is a programming framework for building multi-agent applications."

    async def test():
        agent = AssistantAgent(
            name="assistant",
            model_client=model_client,
            tools=[web_search],
            system_message="Use tools to solve tasks.",
            model_client_stream=True,

        )
        result = await agent.run(task="Find information on AutoGen")
        print(result.messages)

    asyncio.run(test())


# import os
# from openai import OpenAI
#
#
# client = OpenAI(
#     # 若没有配置环境变量，请用百炼API Key将下行替换为：api_key="sk-xxx",
#     api_key=os.getenv("OPENAI_API_KEY"),
#     base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
# )
#
# completion = client.chat.completions.create(
#     # 模型列表：https://help.aliyun.com/zh/model-studio/getting-started/models
#     model="qwen-plus",
#     messages=[
#         {"role": "system", "content": "You are a helpful assistant."},
#         {"role": "user", "content": "你是谁？"},
#     ],
#     # Qwen3模型通过enable_thinking参数控制思考过程（开源版默认True，商业版默认False）
#     # 使用Qwen3开源版模型时，若未启用流式输出，请将下行取消注释，否则会报错
#     # extra_body={"enable_thinking": False},
# )
# print(completion.model_dump_json())

