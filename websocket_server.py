import asyncio
import json
import logging
from fastapi import FastAPI, WebSocket
from fastapi.staticfiles import StaticFiles
from loreal_sales_analysis_system import loreal_team
from autogen_agentchat.messages import ModelClientStreamingChunkEvent, TextMessage
from autogen_agentchat.ui import Console

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()

# 挂载静态文件目录
app.mount("/static2", StaticFiles(directory="static2"), name="static2")

class WebSocketConsole:
    """自定义Console类，将消息发送到WebSocket"""
    
    def __init__(self, websocket):
        self.websocket = websocket
        self.message_cache = {}  # 用于缓存每个agent的最新消息
        
    async def process_message(self, message):
        """处理消息并发送到WebSocket"""
        source = message.source
        content = message.content if hasattr(message, 'content') else message.to_text()
        
        # 决定是否发送此消息
        should_send = True
        
        # 如果是文本消息，检查是否与缓存的流式消息内容相同
        if isinstance(message, TextMessage):
            if source in self.message_cache:
                cached_message = self.message_cache[source]
                if isinstance(cached_message, ModelClientStreamingChunkEvent) and cached_message.content == content:
                    # 如果内容相同，不发送此文本消息
                    should_send = False
                    logger.info(f"跳过与流式消息内容相同的TextMessage: {source}")
        
        # 更新消息缓存
        self.message_cache[source] = message
        
        # 如果需要发送消息
        if should_send:
            # 将消息转换为JSON格式
            message_data = {
                "type": message.__class__.__name__,
                "source": source,
                "content": content,
                "models_usage": {
                    "prompt_tokens": message.models_usage.prompt_tokens if hasattr(message, 'models_usage') and message.models_usage else 0,
                    "completion_tokens": message.models_usage.completion_tokens if hasattr(message, 'models_usage') and message.models_usage else 0
                } if hasattr(message, 'models_usage') and message.models_usage else None
            }
            
            # 发送消息
            await self.websocket.send_json(message_data)
            logger.debug(f"Sent message: {message_data}")

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    logger.info("WebSocket connection accepted")
    
    try:
        # 启动心跳任务
        heartbeat_task = asyncio.create_task(heartbeat(websocket))
        
        # 创建WebSocketConsole实例
        console = WebSocketConsole(websocket)
        
        while True:
            try:
                # 接收前端发送的任务
                task = await websocket.receive_text()
                logger.info(f"Received task: {task}")
                
                if task == "ping":
                    await websocket.send_text("pong")
                    continue
                
                # 重置消息缓存
                console.message_cache = {}
                
                # 运行流式处理
                async for message in loreal_team.run_stream(task=task):
                    try:
                        # 使用WebSocketConsole处理消息
                        await console.process_message(message)
                    except Exception as e:
                        logger.error(f"Error processing message: {str(e)}")
                        # 发送错误消息到前端
                        await websocket.send_json({
                            "type": "Error",
                            "source": "server",
                            "content": f"Error processing message: {str(e)}"
                        })
                
            except Exception as e:
                logger.error(f"Error in message processing loop: {str(e)}")
                # 发送错误消息到前端
                await websocket.send_json({
                    "type": "Error",
                    "source": "server",
                    "content": f"Error in message processing: {str(e)}"
                })
                
    except Exception as e:
        logger.error(f"WebSocket error: {str(e)}")
    finally:
        # 取消心跳任务
        heartbeat_task.cancel()
        try:
            await heartbeat_task
        except asyncio.CancelledError:
            pass
        await websocket.close()
        logger.info("WebSocket connection closed")

async def heartbeat(websocket: WebSocket):
    """发送心跳包保持连接活跃"""
    try:
        while True:
            await asyncio.sleep(30)  # 每30秒发送一次心跳
            if websocket.client_state.CONNECTED:
                await websocket.send_text("heartbeat")
                logger.debug("Heartbeat sent")
    except Exception as e:
        logger.error(f"Heartbeat error: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info") 