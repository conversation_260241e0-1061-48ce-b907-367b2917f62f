<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美妆销售分析系统</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/markdown-it/dist/markdown-it.min.js"></script>
    <style>
        body {
            font-family: 'Roboto', 'Microsoft YaHei', Arial, sans-serif;
            background: #f7f7fa;
            color: #222;
            margin: 0;
            padding: 0;
        }
        .container {
            display: flex;
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 0;
            gap: 32px;
        }
        .sidebar {
            width: 320px;
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            padding: 32px 24px 24px 24px;
            display: flex;
            flex-direction: column;
            gap: 24px;
            border: 1px solid #ececec;
        }
        .sidebar h2 {
            color: #222;
            font-size: 22px;
            font-weight: 700;
            margin-bottom: 18px;
            letter-spacing: 1px;
        }
        .form-group {
            margin-bottom: 18px;
        }
        .form-group label {
            display: block;
            font-size: 15px;
            color: #888;
            margin-bottom: 6px;
        }
        .form-group input[type="text"],
        .form-group textarea {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #ececec;
            border-radius: 6px;
            background: #fff;
            font-size: 15px;
            color: #222;
            margin-bottom: 6px;
        }
        .form-group input[type="file"] {
            margin-bottom: 6px;
        }
        .upload-btn {
            background: #e91e63;
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 10px 0;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            margin-bottom: 10px;
            transition: background 0.2s, color 0.2s;
            box-shadow: 0 1px 4px rgba(233,30,99,0.06);
        }
        .upload-btn:hover:enabled {
            background: #e91e63;
            color: #fff;
        }
        .upload-btn:disabled {
            background: #f7f7fa;
            color: #ccc;
            border: none;
            cursor: not-allowed;
        }
        .data-source-list {
            margin-top: 10px;
        }
        .data-source-list-title {
            color: #e91e63;
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        .data-source-empty {
            color: #bbb;
            font-size: 14px;
            padding: 8px 0;
            background: #f7f7fa;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #ececec;
        }
        .main {
            flex: 1;
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            padding: 32px 36px 24px 36px;
            display: flex;
            flex-direction: column;
            min-width: 0;
            border: 1px solid #ececec;
        }
        .header {
            text-align: center;
            margin-bottom: 18px;
        }
        .header h1 {
            color: #e91e63;
            font-size: 32px;
            font-weight: 900;
            margin: 0 0 8px 0;
            letter-spacing: 2px;
        }
        .header p {
            color: #888;
            font-size: 16px;
            margin: 0;
        }
        .chat-area {
            flex: 1;
            overflow-y: auto;
            background: #fff;
            border-radius: 12px;
            padding: 24px 18px 18px 18px;
            margin-bottom: 18px;
            min-height: 400px;
            border: 1px solid #ececec;
            box-shadow: 0 1px 4px rgba(0,0,0,0.03);
            max-height: 500px;
        }
        .input-area {
            display: flex;
            gap: 10px;
        }
        .input-area input[type="text"] {
            flex: 1;
            padding: 12px 14px;
            border: 1px solid #ececec;
            border-radius: 6px;
            font-size: 16px;
            background: #fff;
            color: #222;
        }
        .input-area button {
            background: #e91e63;
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 0 28px;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: background 0.2s;
            box-shadow: 0 1px 4px rgba(233,30,99,0.06);
        }
        .input-area button:disabled {
            background: #ececec;
            color: #fff;
            cursor: not-allowed;
        }
        .msg-header {
            color: #e91e63;
            font-weight: bold;
            font-size: 17px;
            margin-bottom: 6px;
            letter-spacing: 1px;
        }
        .msg-content {
            background: #f7f7fa;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 15px;
            color: #333;
            word-break: break-all;
            border: 1px solid #ececec;
        }
        .api-tip {
            text-align: right;
            color: #aaa;
            font-size: 13px;
            margin-bottom: 6px;
            margin-top: -10px;
            letter-spacing: 0.5px;
            user-select: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>美妆销售数据分析助手</h1>
        <p>让业务人员轻松获取数据洞察，辅助决策</p>
    </div>
    <div class="container">
        <div class="sidebar">
            <h2>数据源管理</h2>
            <div class="form-group">
                <label>上传数据文件</label>
                <input type="file" disabled>
            </div>
            <div class="form-group">
                <label>数据集名称</label>
                <input type="text" placeholder="如：2025年第一季度销售数据" disabled>
            </div>
            <div class="form-group">
                <label>数据集描述（可选）</label>
                <textarea rows="3" placeholder="数据集的详细描述..." disabled></textarea>
            </div>
            <button class="upload-btn" disabled>上传数据</button>
            <div class="data-source-list">
                <div class="data-source-list-title">可用数据源</div>
                <div class="data-source-empty">没有可用的数据源</div>
            </div>
        </div>
        <div class="main">
            <div class="api-tip">💡 <span>稍等片刻，需要调用api，马上为您生成答案</span></div>
            <div class="chat-area" id="messageContainer"></div>
            <div class="input-area">
                <input type="text" id="taskInput" placeholder="请输入您的问题...">
                <button id="sendButton">发送</button>
            </div>
        </div>
    </div>
    <script>
    // 专家中英文映射
    const expertMap = {
        planning_expert: "📝 任务规划专家",
        query_analysis_expert: "🔎 数据分析解析专家",
        sql_generator_expert: "💻 SQL语句生成专家",
        sql_executor_expert: "🗄️ SQL语句执行专家",
        visualization_expert: "📊 数据可视化专家",
        data_analysis_expert: "📈 数据分析专家",
        knowledge_retrieval_expert: "📚 知识检索专家",
        termination_decision_expert: "💡 问题建议"
    };
    // 特殊头部映射
    const specialHeaderMap = {
        planning_expert: {
            TextMessage: "分析计划"
        },
        data_analysis_expert: {
            TextMessage: "分析结果"
        },
        visualization_expert: {
            TextMessage: "可视化结果",
            Message: "可视化结果"
        }
    };
    const messageContainer = document.getElementById('messageContainer');
    const taskInput = document.getElementById('taskInput');
    const sendButton = document.getElementById('sendButton');
    const md = window.markdownit();
    let ws = null;
    let reconnectAttempts = 0;
    const MAX_RECONNECT_ATTEMPTS = 5;
    let heartbeatInterval = null;
    let streamingBlock = null; // 只保留一个流式块

    // 工具函数
    function scrollToBottom() {
        setTimeout(() => {
            messageContainer.scrollTop = messageContainer.scrollHeight;
        }, 50);
    }
    function createMessageBlock(headerText, type, source) {
        const block = document.createElement('div');
        block.className = 'msg-block';
        block.style.marginBottom = '18px';
        // header
        const header = document.createElement('div');
        header.className = 'msg-header';
        // 为用户和系统加表情
        if (source === 'user') {
            header.textContent = '🧑‍💻 ' + headerText;
        } else if (source === 'termination_decision_expert') {
            header.textContent = '💡 ' + headerText;
        } else {
            header.textContent = headerText;
        }
        block.appendChild(header);
        // content
        const content = document.createElement('div');
        content.className = 'msg-content';
        block.appendChild(content);
        messageContainer.appendChild(block);
        scrollToBottom();
        return {block, header, content};
    }
    function renderMarkdown(content, container) {
        container.innerHTML = md.render(content);
    }
    function renderError(msg) {
        const div = document.createElement('div');
        div.style.color = '#f14c4c';
        div.style.margin = '12px 0';
        div.textContent = msg;
        messageContainer.appendChild(div);
        scrollToBottom();
    }
    // 渲染 ECharts 图表
    function renderECharts(jsonStr, container) {
        let json;
        try {
            json = JSON.parse(jsonStr);
        } catch {
            container.innerHTML = '<span style="color:#f14c4c">可视化配置解析失败</span>';
            return;
        }
        // 创建图表容器
        const chartDiv = document.createElement('div');
        chartDiv.style.width = '100%';
        chartDiv.style.height = '360px';
        container.innerHTML = '';
        container.appendChild(chartDiv);
        // 生成option
        let option = {};
        if (json.type === 'pie') {
            option = {
                title: {text: json.config.title, left: 'center'},
                tooltip: {trigger: 'item'},
                legend: {orient: 'vertical', left: 'left'},
                series: [{
                    name: json.config.seriesName,
                    type: 'pie',
                    radius: '60%',
                    data: json.data || [],
                    emphasis: {
                        itemStyle: {shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)'}
                    }
                }]
            };
        } else if (json.type === 'table') {
            // 渲染表格
            let html = `<div style='overflow-x:auto'><table style='width:100%;border-collapse:collapse;'>`;
            html += `<tr>`;
            for (const col of json.config.columns) {
                html += `<th style='border:1px solid #e91e63;padding:6px 8px;background:#fce4ec;'>${col}</th>`;
            }
            html += `</tr>`;
            if (json.data) {
                for (const row of json.data) {
                    html += `<tr>`;
                    for (const col of json.config.columns) {
                        html += `<td style='border:1px solid #f8bbd0;padding:6px 8px;'>${row[col] ?? ''}</td>`;
                    }
                    html += `</tr>`;
                }
            }
            html += `</table></div>`;
            container.innerHTML = html;
            return;
        } else {
            // 其他类型，简单渲染option
            option = json.config || {};
        }
        const chart = echarts.init(chartDiv);
        chart.setOption(option);
    }
    // 解析content中的json
    function extractJsonFromContent(content) {
        const match = content.match(/```json[\s\S]*?({[\s\S]*?})[\s\S]*?```/);
        if (match) return match[1];
        // 兼容没有markdown包裹的情况
        const match2 = content.match(/({[\s\S]*})/);
        if (match2) return match2[1];
        return null;
    }
    // WebSocket 相关
    function startHeartbeat() {
        if (heartbeatInterval) clearInterval(heartbeatInterval);
        heartbeatInterval = setInterval(() => {
            if (ws && ws.readyState === WebSocket.OPEN) ws.send('ping');
        }, 30000);
    }
    function stopHeartbeat() {
        if (heartbeatInterval) clearInterval(heartbeatInterval);
        heartbeatInterval = null;
    }
    function connectWebSocket() {
        if (ws) ws.close();
        ws = new WebSocket('ws://localhost:8000/ws');
        ws.onopen = () => {
            sendButton.disabled = false;
            reconnectAttempts = 0;
            startHeartbeat();
        };
        ws.onmessage = (event) => {
            let data;
            try {
                data = JSON.parse(event.data);
            } catch {
                return;
            }
            if (data === 'pong' || data === 'heartbeat') return;
            // 过滤 error message
            if (data.type === 'Error' || (typeof data.content === 'string' && data.content.startsWith('Error processing message:'))) {
                return;
            }
            // 流式消息
            if (data.type === 'ModelClientStreamingChunkEvent') {
                if (!streamingBlock) {
                    streamingBlock = createMessageBlock(expertMap[data.source] || data.source, data.type, data.source);
                    streamingBlock.content.textContent = '';
                    // 为 termination_decision_expert 添加特殊标记
                    if (data.source === 'termination_decision_expert') {
                        streamingBlock.isTerminationExpert = true;
                    }
                    // 为 visualization_expert 添加特殊标记
                    if (data.source === 'visualization_expert') {
                        streamingBlock.isVisualizationExpert = true;
                    }
                }
                streamingBlock.content.textContent += data.content;
                scrollToBottom();
                return;
            }
            // 最终消息
            if (data.type === 'TextMessage' || data.type === 'Message') {
                // termination_decision_expert 特殊处理
                if (data.source === 'termination_decision_expert') {
                    // 如果有流式块且是 termination_decision_expert，则覆盖其内容
                    if (streamingBlock && streamingBlock.isTerminationExpert) {
                        // 用 markdown 渲染覆盖之前的流式输出
                        renderMarkdown(data.content, streamingBlock.content);
                        streamingBlock = null;
                    } else {
                        // 如果没有流式块，直接创建新的消息块
                        const {block, content} = createMessageBlock(expertMap[data.source] || data.source, data.type, data.source);
                        renderMarkdown(data.content, content);
                    }
                    scrollToBottom();
                    // 允许继续提问
                    sendButton.disabled = false;
                    return;
                }

                // visualization_expert 特殊处理
                if (data.source === 'visualization_expert') {
                    try {
                        // 尝试直接解析 content 为 JSON
                        let jsonData = null;
                        if (typeof data.content === 'string') {
                            // 尝试从 markdown 代码块中提取 JSON
                            const jsonMatch = data.content.match(/```json\n([\s\S]*?)\n```/) || 
                                           data.content.match(/```\n([\s\S]*?)\n```/) ||
                                           data.content.match(/({[\s\S]*})/);
                            if (jsonMatch) {
                                jsonData = JSON.parse(jsonMatch[1]);
                            } else {
                                // 如果没有代码块，尝试直接解析整个内容
                                jsonData = JSON.parse(data.content);
                            }
                        } else if (typeof data.content === 'object') {
                            jsonData = data.content;
                        }

                        if (jsonData) {
                            // 如果有流式块且是 visualization_expert，则覆盖其内容
                            if (streamingBlock && streamingBlock.isVisualizationExpert) {
                                // 创建图表容器
                                const chartDiv = document.createElement('div');
                                chartDiv.style.width = '100%';
                                chartDiv.style.height = '360px';
                                streamingBlock.content.innerHTML = '';
                                streamingBlock.content.appendChild(chartDiv);

                                // 初始化并渲染图表
                                const chart = echarts.init(chartDiv);
                                chart.setOption(jsonData);
                                
                                // 添加响应式支持
                                window.addEventListener('resize', () => {
                                    chart.resize();
                                });
                                
                                streamingBlock = null;
                            } else {
                                // 如果没有流式块，创建新的消息块
                                const {block, content} = createMessageBlock(expertMap[data.source] || data.source, data.type, data.source);
                                
                                // 创建图表容器
                                const chartDiv = document.createElement('div');
                                chartDiv.style.width = '100%';
                                chartDiv.style.height = '360px';
                                content.innerHTML = '';
                                content.appendChild(chartDiv);

                                // 初始化并渲染图表
                                const chart = echarts.init(chartDiv);
                                chart.setOption(jsonData);
                                
                                // 添加响应式支持
                                window.addEventListener('resize', () => {
                                    chart.resize();
                                });
                            }
                        } else {
                            // 如果没有有效的 JSON 数据，使用 markdown 渲染
                            if (streamingBlock && streamingBlock.isVisualizationExpert) {
                                renderMarkdown(data.content, streamingBlock.content);
                                streamingBlock = null;
                            } else {
                                const {block, content} = createMessageBlock(expertMap[data.source] || data.source, data.type, data.source);
                                renderMarkdown(data.content, content);
                            }
                        }
                    } catch (error) {
                        console.error('Error rendering chart:', error);
                        // 出错时使用 markdown 渲染
                        if (streamingBlock && streamingBlock.isVisualizationExpert) {
                            renderMarkdown(data.content, streamingBlock.content);
                            streamingBlock = null;
                        } else {
                            const {block, content} = createMessageBlock(expertMap[data.source] || data.source, data.type, data.source);
                            renderMarkdown(data.content, content);
                        }
                    }
                    
                    scrollToBottom();
                    // 允许继续提问
                    sendButton.disabled = false;
                    return;
                }

                // 其他专家的处理：移除流式块
                if (streamingBlock && streamingBlock.block && streamingBlock.block.parentNode) {
                    streamingBlock.block.parentNode.removeChild(streamingBlock.block);
                    streamingBlock = null;
                }
                // 不再渲染 source: 'user' 的消息
                if (data.source === 'user') {
                    return;
                }
                // 其他消息
                const {block, content} = createMessageBlock(expertMap[data.source] || data.source, data.type, data.source);
                renderMarkdown(data.content, content);
                scrollToBottom();
                // 允许继续提问
                sendButton.disabled = false;
                return;
            }
        };
        ws.onclose = () => {
            sendButton.disabled = true;
            stopHeartbeat();
            if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                reconnectAttempts++;
                setTimeout(connectWebSocket, 1000 * reconnectAttempts);
            } else {
                renderError('连接已断开，请刷新页面重试');
            }
        };
        ws.onerror = (error) => {
            renderError('连接发生错误，请刷新页面重试');
        };
    }
    sendButton.addEventListener('click', () => {
        const task = taskInput.value.trim();
        if (task && ws && ws.readyState === WebSocket.OPEN) {
            // 立即展示用户消息
            const {block: userBlock, content: userContent} = createMessageBlock('用户', 'TextMessage', 'user');
            userContent.textContent = task;
            scrollToBottom();
            ws.send(task);
            taskInput.value = '';
            sendButton.disabled = true;
        }
    });
    taskInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') sendButton.click();
    });
    // 初始连接
    connectWebSocket();
    </script>
</body>
</html> 